#!/usr/bin/env python3
"""
从备份文件恢复效果不佳的精简结果
"""

import json
import sys
import re

def count_words(text: str) -> int:
    """计算英文单词数量"""
    if not text or not text.strip():
        return 0
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 按空格分割并过滤空字符串
    words = [word for word in text.split() if word.strip()]
    
    return len(words)

def analyze_and_restore(current_file: str, backup_file: str, deviation_threshold: float = 25.0):
    """
    分析当前文件的调整效果，恢复效果不佳的条目
    
    Args:
        current_file: 当前调整后的文件
        backup_file: 备份文件
        deviation_threshold: 偏差阈值，超过此值的条目将被恢复
    """
    
    # 读取当前文件和备份文件
    with open(current_file, 'r', encoding='utf-8') as f:
        current_data = json.load(f)
    
    with open(backup_file, 'r', encoding='utf-8') as f:
        backup_data = json.load(f)
    
    restored_count = 0
    analysis_results = []
    
    print(f"分析文件: {current_file}")
    print(f"备份文件: {backup_file}")
    print(f"偏差阈值: {deviation_threshold}%")
    print("-" * 60)
    
    for i, (current_item, backup_item) in enumerate(zip(current_data, backup_data)):
        if current_item['id'] != backup_item['id']:
            print(f"警告: 条目 {i} 的ID不匹配")
            continue
        
        # 计算当前偏差
        current_words = count_words(current_item['answer'])
        word_limit = current_item['word_limit']
        current_deviation = ((current_words - word_limit) / word_limit) * 100
        
        # 计算备份偏差
        backup_words = count_words(backup_item['answer'])
        backup_deviation = ((backup_words - word_limit) / word_limit) * 100
        
        analysis_results.append({
            'id': current_item['id'],
            'word_limit': word_limit,
            'backup_words': backup_words,
            'backup_deviation': backup_deviation,
            'current_words': current_words,
            'current_deviation': current_deviation,
            'improvement': abs(backup_deviation) - abs(current_deviation)
        })
        
        # 判断是否需要恢复
        should_restore = False
        reason = ""
        
        if abs(current_deviation) > deviation_threshold:
            should_restore = True
            reason = f"偏差过大 ({current_deviation:.1f}%)"
        elif abs(current_deviation) > abs(backup_deviation):
            should_restore = True
            reason = f"效果变差 ({backup_deviation:.1f}% → {current_deviation:.1f}%)"
        
        if should_restore:
            current_data[i]['answer'] = backup_item['answer']
            restored_count += 1
            print(f"恢复 {current_item['id']}: {reason}")
            print(f"  {backup_words}/{word_limit} 字 ({backup_deviation:.1f}%) → {current_words}/{word_limit} 字 ({current_deviation:.1f}%)")
        else:
            print(f"保留 {current_item['id']}: 效果良好")
            print(f"  {backup_words}/{word_limit} 字 ({backup_deviation:.1f}%) → {current_words}/{word_limit} 字 ({current_deviation:.1f}%)")
    
    # 保存恢复后的文件
    if restored_count > 0:
        restored_file = current_file.replace('.json', '_restored.json')
        with open(restored_file, 'w', encoding='utf-8') as f:
            json.dump(current_data, f, ensure_ascii=False, indent=4)
        
        print(f"\n恢复完成!")
        print(f"恢复了 {restored_count} 个条目")
        print(f"结果保存到: {restored_file}")
    else:
        print(f"\n无需恢复，所有条目效果都可接受")
    
    # 显示统计信息
    print(f"\n统计信息:")
    good_results = [r for r in analysis_results if abs(r['current_deviation']) <= 10]
    acceptable_results = [r for r in analysis_results if 10 < abs(r['current_deviation']) <= 25]
    poor_results = [r for r in analysis_results if abs(r['current_deviation']) > 25]
    
    print(f"  优秀结果 (偏差 ≤10%): {len(good_results)} 个")
    print(f"  可接受结果 (偏差 10-25%): {len(acceptable_results)} 个")
    print(f"  不佳结果 (偏差 >25%): {len(poor_results)} 个")
    
    return restored_count

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python restore_from_backup.py <current_file> [deviation_threshold]")
        print("  current_file: 当前调整后的JSON文件")
        print("  deviation_threshold: 偏差阈值 (默认25.0)")
        print("  备份文件会自动查找 <current_file>.ai_backup")
        sys.exit(1)
    
    current_file = sys.argv[1]
    backup_file = current_file + '.ai_backup'
    
    deviation_threshold = 25.0
    if len(sys.argv) > 2:
        try:
            deviation_threshold = float(sys.argv[2])
        except ValueError:
            print("警告: 偏差阈值无效，使用默认值 25.0")
    
    try:
        restored_count = analyze_and_restore(current_file, backup_file, deviation_threshold)
    except FileNotFoundError as e:
        print(f"错误: 文件未找到 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)
